// Event bubbling

// const { useCallback } = require("react")

// document.querySelector (".child").addEventListener("click", (e) => {
//             e.stopPropagation()
//             alert("child was clicked")
//         } )
//         document.querySelector(".child-container").addEventListener("click", (e) => {
//             e.stopPropagation()
//             alert("child container was clicked")
//         } )
//         document.querySelector(".container").addEventListener("click", (e) => {
//             e.stopPropagation()
//             alert("container was clicked")
//         } )







// Callbacks

// sum(displayPage, 1, 2);

// function sum(callback, x, y) {
//     let result = x + y;
//     callback(result);
// }

// function displayPage(result) {
//     document.getElementById("myH1").textContent = result;
// }
