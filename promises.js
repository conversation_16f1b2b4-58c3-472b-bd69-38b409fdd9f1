function walkTheDog() {
return new Promise((resolve, reject) => {
setTimeout(() => {
resolve("You walk the dog");
}, 900);
});
}

function cleanKitchen() {
return new Promise((resolve, reject) => {
setTimeout(() => {
resolve("Clean the kitchen");
}, 600);
});
}

function trash() {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve("Remove the trash");
        }, 300);
    });
}

walkTheDog().then(value => { console.log(value); return cleanKitchen() })
    .then(value => { console.log(value); return trash() })
    .then(value => { console.log(value); console.log("You finished the task") })
